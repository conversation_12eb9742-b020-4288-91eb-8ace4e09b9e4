import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Map, PlotDraw, DrawHelper, Util } from 'yth-map';
import { YTHToast } from 'yth-ui';
import markImg from '@/assets/position.png';
import './MapDraw.less';
import { Github } from 'react-color/lib/components/github/Github';
import { Button, Input, Spin, message } from 'antd';
import dicParams from '@/utils/dicParams';
import { queryByParkCode } from '@/service/baseModuleApi';
import { Props, SORN } from '../MapCard/components/mapTypes';
import tdt from './images/tdt_img.jpg';
// useDebounce 防抖
const useDebounce = <T,>(value: T, delay?: number): T => {
  const [debouncedValue, setDebouncedValue] = useState(value);
  useEffect(() => {
    const timeout = setTimeout(() => setDebouncedValue(value), delay);
    return () => clearTimeout(timeout);
  }, [value, delay]);
  return debouncedValue;
};

const map = new Map();

// 地图绘制
const MapCard = forwardRef((props: Props, ref) => {
  const [initLoading, setInitLoading] = useState<boolean>();

  // 地图初始化
  const layers = [
    {
      name: '天地图影像',
      image: tdt,
      show: true,
      list: [
        // 影像
        {
          url: 'http://t0.tianditu.com/img_c/wmts?tk=c8b337de7f212a5178a3b1aa7ec3856b&service=wmts&request=GetTile&version=1.0.0&LAYER=img&tileMatrixSet=c&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles',
          layer: 'img',
          style: 'default',
          format: 'image/png',
          tileMatrixSetID: 'c',
          minimumLevel: 1,
          maximumLevel: 18,
          tilingScheme: 2,
        },
        // 影像注记
        {
          url: 'http://t0.tianditu.com/cia_c/wmts?tk=c8b337de7f212a5178a3b1aa7ec3856b&service=wmts&request=GetTile&version=1.0.0&LAYER=cia&tileMatrixSet=c&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles',
          layer: 'cia',
          style: 'default',
          format: 'image/png',
          tileMatrixSetID: 'c',
          minimumLevel: 1,
          maximumLevel: 18,
          tilingScheme: 2,
        },
      ],
    },
    {
      name: '天地图(矢量)',
      image: tdt,
      show: false,
      list: [
        // 矢量
        {
          url: 'http://t0.tianditu.com/vec_c/wmts?tk=52fea9a68c4c16c39dd759ab4fce602f&service=wmts&request=GetTile&version=1.0.0&LAYER=vec&tileMatrixSet=c&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles',
          layer: 'vec',
          style: 'default',
          format: 'image/png',
          tileMatrixSetID: 'c',
          minimumLevel: 1,
          maximumLevel: 18,
          tilingScheme: 2,
        },
        // 矢量注记
        {
          url: 'http://t0.tianditu.com/cva_c/wmts?tk=52fea9a68c4c16c39dd759ab4fce602f&service=wmts&request=GetTile&version=1.0.0&LAYER=cva&tileMatrixSet=c&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles',
          layer: 'cva',
          style: 'default',
          format: 'image/png',
          tileMatrixSetID: 'c',
          minimumLevel: 1,
          maximumLevel: 18,
          tilingScheme: 2,
        },
      ],
    },
  ];
  const loadScriptAsync = (callback) => {
    const script = document.createElement('script');
    script.src = 'http://api.tianditu.gov.cn/api?v=4.0&tk=' + dicParams.mapKey;
    script.async = true;
    script.onload = callback;
    document.body.appendChild(script);
  };
  useImperativeHandle(props.onRef, () => {
    return {
      resetMap: resetMap,
      destroyMap: destroyMap,
      getDrawData: getDrawData,
    };
  });

  const initMap = async () => {
    setInitLoading(true);
    let initPoint: any = dicParams.mapCenterStr;
    console.log(props.position);
    if (props.position && props.position.x) {
      initPoint = props.position;
    } else if (props.areaList && props.areaList !== '') {
      try {
        const areaListObj = JSON.parse(props.areaList);
        if (areaListObj && Array.isArray(areaListObj) && areaListObj.length > 0) {
          initPoint = {
            x: areaListObj[0].list[0],
            y: areaListObj[0].list[1],
            z: areaListObj[0].list[2],
          };
        }
      } catch (err) {
        console.log(err);
      }
    } else {
      const park = await queryByParkCode();
      const mapCenter = park?.center ? JSON.parse(park?.center) : {};
      initPoint = {
        x: mapCenter?.x ?? 0,
        y: mapCenter?.y ?? 0,
        z: 10,
      };
    }

    map.initMap({
      container: 'map', // 地图承载容器
      sceneModePicker: true, // 二三维选择按钮-显示控制
      sceneModeChose: 3, // 显示模式 三维: 3 二维: 2  默认值: 3
      positionDisplay: true, // 右下角经纬度显示 默认值: true
      compassDisplay: true, // 罗盘显示 默认值: true
      hostAddr: 'http://***************:8096/check',
      // 地图右键点击事件回调，返回参数(position, pt)
      rightClickCall: (position, pt) => {
        console.log(position);
        console.log(pt);
      },
      components: true,

      // 初始位置,地图重置(replace)时用到
      initPlace: {
        point: initPoint,
      },
      layersPro: layers,
      // 地图重置按钮   默认视角, 罗盘中恢复的视角，默认是中国范围
      defaultView: {
        rect: [112.96100967885242, 28.194319720664925, 112.97098015969033, 28.198415260838136],
      },
      //完成地图加载
      callback: () => {
        setInitLoading(false);
        drawAreaByDefault();
        map.flyObject(initPoint);
        map.setMapBackground('天地图影像');

        map.plotDraw = new PlotDraw({
          map: map,
          callback: (geo) => {
            console.log(geo, 'geo');
          },
          editable: false,
        });
        const drawHelper = new DrawHelper({ map: map });
      },
    });
  };

  // 点位标记
  const [position, setPosition] = useState<SORN[]>(['', '', '']);
  const [marker, setMarker] = useState<any>();
  const colorSelectRef = useRef<any>(''); // 标记当前选中的颜色
  const [colorSelect, setColorSelect] = useState<any>('');
  const [positionName, setPositionName] = useState<string>('');
  const [positionCode, setPositionCode] = useState<string>('');
  const [tipLabel, setTipLabel] = useState<string>('请选择颜色并开始绘制'); //设置提示词
  const polygonListRef = useRef<any>([]); // 记录当前绘制的多边形

  // 获取绘制数据
  const getDrawData = () => {
    const currentPolygons = polygonListRef.current;
    if (props.areaDarw) {
      currentPolygons[0].partitionName = positionName;
      currentPolygons[0].partitionCode = positionCode;
      polygonListRef.current = currentPolygons;
    }

    return polygonListRef.current;
  };

  useEffect(() => {
    if (!position[0] || !position[1]) return;
    console.log(position, 'position');
    console.log(transferPoint(position));
    map.flyObject(transferPoint(position));
    markPositon(transferPoint(position));
    if (props.position == position) return;
    props.onCoordChange!({ lng: position[0], lat: position[1] });
  }, [useDebounce(position, 200)]);

  useEffect(() => {
    initMap();
  }, []);

  useEffect(() => {
    if (!map) return;
    if (props.position instanceof Array) {
      console.log(props.position, 'prop.position');
      setPosition(props.position);
    }
  }, [map, props.position]);

  // 根据关键字搜索
  const searchLocation = async () => {
    const resultsList: any = document.getElementById('results');
    resultsList.innerHTML = '';
    const value: any = document.getElementById('inputSearch')!.value;
    const rUrl = `https://api.tianditu.gov.cn/v2/search?postStr={"keyWord":"${value}","level":"11","mapBound":"102.546150,24.396308,103.157679,25.132221","queryType":"1","count":"10","start":"0"}&type=query&tk=${dicParams.mapKey}`;
    fetch(rUrl, {
      referrerPolicy: 'strict-origin-when-cross-origin',
      body: null,
      method: 'GET',
      mode: 'cors',
      headers: {},
      credentials: 'omit',
    })
      .then((res) => {
        //console.log(res);
        return res.json();
      })
      .then((data) => {
        console.log(data);
        localSearchResult(data);
      });
  };

  const localSearchResult = (result) => {
    const resultsList: any = document.getElementById('results');
    console.log(result);
    if (result && result.pois && Array.isArray(result.pois)) {
      const locations = result.pois;
      locations.forEach((location, index) => {
        const listItem = document.createElement('li');
        listItem.textContent = `${index + 1}: ${location.name}`;
        listItem.addEventListener('click', () => {
          //alert(location.lonlat); // Show the inner info
          const nLoc: any = location.lonlat.split(',');
          console.log(nLoc);
          map.flyObject({ x: nLoc[0], y: nLoc[1], z: 155 });
        });
        resultsList.appendChild(listItem);
      });
    } else {
      message.error('无数据');
    }
  };
  // 清空搜索结果
  const clearSearch = () => {
    const searchInput: any = document.getElementById('inputSearch')!;
    const resultsList: any = document.getElementById('results');
    searchInput.value = ''; // Clear search input
    resultsList.innerHTML = ''; // Clear search results
  };

  // 是否是手机端
  const [isMobileIphone, setIsMobileIphone] = useState(document.body.clientWidth < 640);
  window.onresize = () => {
    setIsMobileIphone(document.body.clientWidth < 640);
  };
  /**
   * @abstract 根据传参绘制区域
   */
  const drawAreaByDefault = () => {
    if (props.areaList && props.areaList !== '') {
      try {
        const areaListObj = JSON.parse(props.areaList);
        polygonListRef.current = areaListObj;
        console.log(areaListObj, 'areaListObj');
        areaListObj.forEach((item) => {
          if (props.areaDarw && item.partitionName) {
            setPositionName(item.partitionName);
          }
          if (item.partitionCode) {
            setPositionCode(item.partitionCode);
          }
          if (props.areaDarw) {
            map.layer.addPolygon({
              positions: item.list,
              fill: true,
              outlineColor: item.color,
              fillColor: item.color,
            });
          } else {
            map.layer.addPolyline({
              positions: item.list,
              fill: true,
              outlineColor: item.color,
              fillColor: item.color,
            });
          }
        });
      } catch (err) {
        console.log(err);
      }
    } else {
      console.log('');
    }
  };
  // 重置地图
  const resetMap = (posClear = false) => {
    setInitLoading(false);
    if (props.position && !posClear) return;
    setMarker(null);
    setPosition(['', '']);
    map.resetPlace();
  };

  // 卸载地图
  const destroyMap = () => {
    resetMap();
    map.resetPlace();
  };

  //选择位置
  const selectPosition = () => {
    setPosition([]);
    map.layer.clearAll();
    map.plotDraw.activate(0, {
      callback: (geo, pos) => {
        setPosition(geo.geometry.coordinates[0][0]);
        markPositon(transferPoint(geo.geometry.coordinates[0][0]));
      },
    });
  };

  // 绘制区域
  const drawArea = () => {
    setTipLabel('正在进行绘制');
    console.log(map.getCameraInfo());
    let drawtype = props.areaDarw ? 2 : 1;
    map.plotDraw.activate(drawtype, {
      callback: (geo, pos) => {
        // setPosition(geo.geometry.coordinates[0][0]);
        console.log(geo.geometry.coordinates);
        const psList: any = [];
        geo.geometry.coordinates[0].forEach((item) => {
          psList.push(item[0]);
          psList.push(item[1]);
          psList.push(Util.getHeight({ x: item[0], y: item[1], map: map }));
          //psList.push(item[2]);
        });
        console.log(psList);
        const currentPolygons = polygonListRef.current;
        if (props.areaDarw) {
          map.layer.addPolygon({
            positions: psList,
            fill: true,
            outlineColor: colorSelectRef.current,
            fillColor: colorSelectRef.current,
          });
        } else {
          map.layer.addPolyline({
            positions: psList,
            fill: true,
            outlineColor: colorSelectRef.current,
            fillColor: colorSelectRef.current,
          });
        }

        currentPolygons.push({
          list: psList,
          color: colorSelectRef.current,
        });
        polygonListRef.current = currentPolygons;

        map.plotDraw.deactivate();
        setTipLabel('请选择颜色并继续绘制');
      },
    });
  };

  const markPositon = (value) => {
    map.layer.clearAll();
    // 绘制
    map.layer.addMarker({
      layerName: 'positionSele',
      point: value,
      img: markImg,
      scale: 0.3,
      offset: { x: 0, y: -15 },
    });
  };

  // 转换位置
  const transferPoint = (value) => {
    const position = {
      x: value[0],
      y: value[1],
      z: value[2] ?? 1,
    };
    return position;
  };

  //颜色画笔更改
  const colorChange = (color) => {
    // 可以绘制多条线路
    // if(polygonListRef.current.length > 0 && !props.isOverlay){
    //   console.log(polygonListRef.current ,'draw')
    //   YTHToast.show({
    //     type: "warning",
    //     messageText: "请先清空已绘制内容，再进行绘制",
    //     p_props: { duration: 10 },
    //     m_props: { duration: 2000 },
    //   });
    //   return
    // }
    if (props.areaDarw && positionName == '') {
      YTHToast.show({
        type: 'warning',
        messageText: '请先输入区域名字，再进行区域绘制',
        p_props: { duration: 10 },
        m_props: { duration: 2000 },
      });
      return;
    }
    if (props.areaDarw && positionCode == '') {
      YTHToast.show({
        type: 'warning',
        messageText: '请先输入区域编号，再进行区域绘制',
        p_props: { duration: 10 },
        m_props: { duration: 2000 },
      });
      return;
    }
    setColorSelect(color.hex);
    colorSelectRef.current = color.hex;
    drawArea();
  };

  // 确认按钮
  const confirmBotton = () => {
    if (!positionName || !positionCode || !colorSelectRef.current) {
      YTHToast.show({
        type: 'warn',
        messageText: '请先输入名字、编号和选择颜色！',
        p_props: { duration: 10 },
        m_props: { duration: 3000 },
      });
      return;
    }
    let params = {
      partitionColor: colorSelectRef.current,
    };
    props.confirmButton!(params);
  };

  // 取消按钮
  const cancelbotton = () => {
    polygonListRef.current = [];
    map.layer.clearAll();
    setColorSelect('');
  };

  return (
    <Spin spinning={initLoading} size="large" wrapperClassName="spinWrap">
      <main className="map-container">
        <div
          id="map"
          style={{
            width: props.mapConfig?.width || '100%',
            height: props.mapConfig?.height || '100%',
          }}
        ></div>
        <div className="toolbar">
          {props.areaDarw && (
            <div className="inputbar">
              <Input
                value={positionName}
                style={{ width: '250px' }}
                placeholder="请输入区域名称"
                type="string"
                onChange={(e) => {
                  setPositionName(e.target.value);
                }}
              />

              <Input
                value={positionCode}
                style={{ width: '250px', marginLeft: '5px' }}
                placeholder="请输入区域编号"
                type="string"
                onChange={(e) => {
                  setPositionCode(e.target.value);
                }}
              />
            </div>
          )}
          <div style={{ display: 'flex', alignItems: 'cneter' }}>
            <div className="info ant-card ant-card-bordered">
              <div className="item">
                <div className="search-bar">
                  <div className="search-bar-input">
                    <input
                      id="inputSearch"
                      className="inputArea"
                      placeholder="请输入搜索内容"
                    ></input>
                    <Button type="default" className="map-search-btn" onClick={searchLocation}>
                      搜索
                    </Button>
                    <Button className="map-clear-btn" type="default" onClick={clearSearch}>
                      重置
                    </Button>
                  </div>
                  <div className="search-bar-result">
                    <div id="results"></div>
                  </div>
                </div>
                {colorSelect && colorSelect !== '' && (
                  <div className="color" style={{ backgroundColor: colorSelect }}></div>
                )}
                <div style={{ marginLeft: '20px', color: 'red' }}>{tipLabel}</div>
              </div>
            </div>
          </div>
        </div>
        {props.operateType !== 'view' && (
          <div className="colorArea">
            <Github
              triangle={'hide'}
              colors={[
                'rgba(255,0,0,0.6)',
                'rgba(255,197,61,0.6)',
                'rgba(0,0,255,0.6)',
                'rgba(0,255,0,0.6)',
              ]}
              styles={{ input: { display: 'none' } }}
              onChange={(color) => {
                colorChange(color);
              }}
            />
            <Button
              className="reset-button"
              type="default"
              onClick={() => {
                cancelbotton();
              }}
            >
              清空
            </Button>
          </div>
        )}
      </main>
    </Spin>
  );
});

export default MapCard;
